// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 控制指令实体
/// </summary>
[SugarTable("CONTROL_CMD")]
public partial class ControlCmdEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [SugarColumn(ColumnName = "CMD_NAME")]
    public string CmdName { get; set; }

    /// <summary>
    /// 设备id
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 通讯介质
    /// 1:MQTT
    /// </summary>
    [SugarColumn(ColumnName = "COMMUNICATION_MEDIUM")]
    public int CommunicationMedium { get; set; }

    /// <summary>
    /// 指令内容
    /// </summary>
    [SugarColumn(ColumnName = "CMD_CONTENT")]
    public string CmdContent { get; set; }

    /// <summary>
    /// 编码方式
    /// 1：JSON 2:HEX
    /// </summary>
    [SugarColumn(ColumnName = "ENCODE")]
    public int Encode { get; set; }

    /// <summary>
    /// 区域id
    /// 参数传输
    /// </summary>
    [SugarColumn(ColumnName = "AreaId")]
    public int? AreaId { get; set; }

    /// <summary>
    /// 设备分类
    /// 参数传输
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_CATEGORY")]
    public int? DeviceCategory { get; set; }

    /// <summary>
    /// 是否启用APP控制
    /// 0:否 1:是
    /// </summary>
    [SugarColumn(ColumnName = "IS_APP_CONTROL")]
    public int IsAppControl { get; set; } = 1;

    
}
