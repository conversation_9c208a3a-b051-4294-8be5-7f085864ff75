﻿using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Admin.Communication.Mqtt.Services;
using Admin.Communication.Mqtt.Examples;
using Admin.Communication.Mqtt.Handlers;
using Admin.Communication.Mqtt.Extensions;
using Admin.Communication.Mqtt.Workers;
using Admin.Communication.Extensions;
using Admin.Communication.Modbus;
using Admin.Communication.Modbus.Workers;
using Admin.Communication.Alarm.Detectors;
using Admin.Communication.Alarm.Services;
using Admin.Communication.DeviceOnline.Workers;
using Admin.Communication.DeviceOnline.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Modularity;
using Volo.Abp;
using Volo.Abp.BackgroundWorkers;
using Microsoft.Extensions.Logging;

namespace Admin.Communication
{
    public class AdminCommunicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();

            // 注册MQTT服务
            ConfigureMqttServices(context, configuration);
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            // 配置服务提供者以支持非DI环境中的服务获取
            // ServiceProviderHelper.SetServiceProvider(context.ServiceProvider);

            var logger = context.ServiceProvider.GetRequiredService<ILogger<AdminCommunicationModule>>();
            logger.LogInformation("Admin Communication Module initialized");

            // 注册MQTT会话清理后台工作器
            context.AddBackgroundWorkerAsync<MqttSessionCleanupWorker>();

            // 启动MQTT工作器（如果存在的话）
            // context.AddBackgroundWorkerAsync<MqttWorker>();

            // 启动Modbus指令调度服务
            context.AddBackgroundWorkerAsync<ModbusInstructionSchedulerService>();

            // 启动设备在线检测后台服务
            context.AddBackgroundWorkerAsync<DeviceOnlineDetectionWorker>();

            // 注册MQTT连接事件处理器
            RegisterMqttConnectionEventHandlers(context);
        }

        private void ConfigureMqttServices(ServiceConfigurationContext context, IConfiguration configuration)
        {
            // 使用扩展方法注册MQTT Broker服务（包含连接管理器）
            context.Services.AddMqttBroker(options =>
            {
                configuration.GetSection("Mqtt:Broker").Bind(options);
                
                // 如果配置文件中没有配置，使用默认值
                if (options.Port == 0) options.Port = 1883;
                if (options.MaxConnections == 0) options.MaxConnections = 1000;
                if (options.ConnectionTimeout == 0) options.ConnectionTimeout = 60;
                if (options.MaxConnectionsPerIp == 0) options.MaxConnectionsPerIp = 100;
                
                // 启用统计功能
                options.EnableStatistics = true;
                options.EnableRetainedMessages = true;
                
                // 如果允许匿名访问，设置为true
                options.AllowAnonymousAccess = configuration.GetValue<bool>("Mqtt:Broker:AllowAnonymousAccess", true);
            });

            // 注册MQTT用户服务
            context.Services.AddTransient<IMqttUserService, MqttUserService>();

            // 注册消息分发器
            context.Services.AddSingleton<IMqttMessageDispatcher, MqttMessageDispatcher>();

            // 注册消息处理管道
            context.Services.AddSingleton<IMqttMessagePipeline, MqttMessagePipeline>();

            // 注册数据解析器服务
            context.Services.AddDataParserServices();

            // 注册Modbus服务
            context.Services.AddTransient<IModbusResponseHandler, ModbusResponseHandler>();

            // 注册告警相关服务
            RegisterAlarmServices(context);

            // 注册示例消息处理器
            RegisterMessageHandlers(context);

            // 注册示例消息转换器和验证器
            RegisterMessageTransformersAndValidators(context);

            // 注册托管服务，使MQTT Broker随应用程序启动而启动
            context.Services.AddHostedService<MqttBrokerHostedService>();
        }



        private void RegisterMessageHandlers(ServiceConfigurationContext context)
        {
            // 注册设备数据处理器
            context.Services.AddTransient<IMqttMessageHandler, DeviceDataHandler>();
        }

        private void RegisterMessageTransformersAndValidators(ServiceConfigurationContext context)
        {
            // 注册消息验证器
            context.Services.AddTransient<IMqttMessageValidator, JsonFormatValidator>();
            context.Services.AddTransient<IMqttMessageValidator, DeviceDataValidator>();

            // 注册消息转换器
            context.Services.AddTransient<IMqttMessageTransformer, MessageRoutingTransformer>();
            context.Services.AddTransient<IMqttMessageTransformer, MessageEncryptionTransformer>();
            context.Services.AddTransient<IMqttMessageTransformer, MessageCompressionTransformer>();
        }

        /// <summary>
        /// 注册告警相关服务
        /// </summary>
        private void RegisterAlarmServices(ServiceConfigurationContext context)
        {
            // 注册告警检测器
            context.Services.AddTransient<ParameterAlarmDetector>();

            // 注册告警服务
            context.Services.AddTransient<AlarmService>();

            // 注册告警事件生成器
            context.Services.AddTransient<AlarmEventGeneratorService>();

            // 注册设备在线检测服务
            context.Services.AddTransient<DeviceOnlineDetectionService>();
        }

        /// <summary>
        /// 注册MQTT连接事件处理器
        /// </summary>
        private void RegisterMqttConnectionEventHandlers(ApplicationInitializationContext context)
        {
            try
            {
                var connectionManager = context.ServiceProvider.GetRequiredService<IMqttConnectionManager>();
                var logger = context.ServiceProvider.GetRequiredService<ILogger<AdminCommunicationModule>>();

                // 订阅连接状态变化事件
                connectionManager.ConnectionStatusChanged += (sender, e) =>
                {
                    try
                    {
                        // 设备连接状态变化 - 后续开发离线告警功能
                        logger.LogInformation("设备连接状态变化: ClientId={ClientId}", e.ClientId);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "处理连接状态变化事件时发生异常: ClientId={ClientId}", e.ClientId);
                    }
                };

                // 订阅连接超时事件
                connectionManager.ConnectionTimeout += (sender, e) =>
                {
                    try
                    {
                        // 连接超时 - 后续开发离线告警功能
                        logger.LogInformation("设备连接超时: ClientId={ClientId}", e.ClientId);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "处理连接超时事件时发生异常: ClientId={ClientId}", e.ClientId);
                    }
                };

                logger.LogInformation("MQTT连接事件处理器注册成功");
            }
            catch (Exception ex)
            {
                var logger = context.ServiceProvider.GetRequiredService<ILogger<AdminCommunicationModule>>();
                logger.LogError(ex, "注册MQTT连接事件处理器时发生异常");
            }
        }
    }
}