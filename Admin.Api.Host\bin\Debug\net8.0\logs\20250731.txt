[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[16:07:53] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[16:07:53] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[16:07:53] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[16:07:54] [INF] Admin.Communication.AdminCommunicationModule 
MQTT连接事件处理器注册成功

[16:07:54] [INF]  
项目当前环境为：Development

[16:07:54] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[16:07:56] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:07:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:52022

[16:07:56] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=123123

[16:07:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[16:08:08] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:08:23] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:08:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:08:23] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:08:23] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:08:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:08:37] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:08:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:08:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:08:37] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:08:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:08:51] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:08:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:08:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:08:51] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:08:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:09:05] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:09:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:09:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:09:05] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:09:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:09:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:09:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:09:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:09:19] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:09:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:09:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:09:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:09:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:09:33] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:09:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:09:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:09:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:09:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:09:47] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:09:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:01] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:01] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:01] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:01] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:01] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:15] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:26] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:26] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:26] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:29] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:29] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:31] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:31] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:31] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:31] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:31] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:33] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:34] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:35] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:35] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:35] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:38] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:38] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:39] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:39] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:39] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:42] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:43] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:43] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:43] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:46] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:46] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:46] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:46] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:46] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:47] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:47] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:47] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:50] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:50] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:50] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:50] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:50] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:51] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:51] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:54] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:54] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:54] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:54] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:55] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:55] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:55] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:58] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:58] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:58] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:58] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:58] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:59] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:10:59] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:10:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:02] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:02] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:02] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:02] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:02] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:03] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:03] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:03] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:06] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:06] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:06] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:06] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:06] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:07] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:07] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:07] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:10] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:10] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:10] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:11] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:11] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:11] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:14] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:15] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:15] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:18] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:19] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:19] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:22] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:22] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:22] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:22] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:22] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:23] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:23] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:23] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:24] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:24] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:24] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:24] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:44] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:44] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:44] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:44] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:44] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:11:58] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:11:58] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:11:58] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:11:58] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:11:58] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:12:12] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:12:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:12:12] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:12:12] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:12:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:12:26] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:12:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:12:26] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:12:26] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:12:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:12:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:12:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:12:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:12:42] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:12:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:12:57] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:12:57] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:12:57] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:12:57] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:12:57] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:13:12] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:13:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[16:13:12] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:13:12] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:13:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[16:38:13] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[16:38:13] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[16:38:13] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[16:38:13] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[16:38:13] [INF] Admin.Communication.DeviceOnline.Workers.DeviceOnlineDetectionWorker 
设备在线检测工作器初始化完成: KeepAliveTimeout=45s, DataReportTimeout=2min, CheckInterval=30s

[16:38:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[16:38:13] [INF] Admin.Communication.AdminCommunicationModule 
MQTT连接事件处理器注册成功

[16:38:14] [INF]  
项目当前环境为：Development

[16:38:14] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[16:38:14] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[16:38:14] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[16:38:14] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[16:38:14] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[16:38:14] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[16:38:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[16:38:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:38:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:52553

[16:38:15] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=123123

[16:38:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[16:38:51] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:38:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:59764

[16:38:51] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:38:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[16:58:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-31T16:56:26.6900142+08:00"

[16:58:13] [INF] Admin.Communication.AdminCommunicationModule 
设备连接超时: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:58:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:58:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:58:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[16:58:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[16:58:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:58:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:60918

[16:58:18] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[16:58:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[16:58:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=6ff75c1feeb140bb9d51756461c3adb0

[17:08:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-31T17:06:26.6874936+08:00"

[17:08:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接超时: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:08:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:08:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:08:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:08:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:08:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:08:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:61632

[17:08:18] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:08:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:08:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=b64777a808194f0393f85cd861fded55

[17:23:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-31T17:21:26.6876846+08:00"

[17:23:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接超时: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:23:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:23:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:23:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:23:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:23:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:23:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:62646

[17:23:18] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:23:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:23:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=a940e249764549ee8fa306d3a74cdd27

[17:33:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-31T17:31:26.6853525+08:00"

[17:33:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接超时: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:33:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:33:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:33:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:33:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:33:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:33:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:63306

[17:33:18] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:33:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:33:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=33fa1e9918c64288bcb355162acfb0de

[17:53:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-31T17:51:26.6899116+08:00"

[17:53:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接超时: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:53:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:53:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:53:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:53:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:53:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:53:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:64768

[17:53:18] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:53:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:53:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=356d4bd2f6e349b8a762d4a4de92510f

[18:03:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-31T18:01:26.6908129+08:00"

[18:03:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接超时: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[18:03:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[18:03:14] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[18:03:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[18:03:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[18:03:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[18:03:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:65519

[18:03:19] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[18:03:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[18:03:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=ff22e81a8de2453589a6e42e36bd1423

